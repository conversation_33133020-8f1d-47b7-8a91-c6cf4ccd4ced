<?php

// التحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: index.php');
        exit();
    }
}

// تسجيل النشاط
function logActivity($pdo, $user_id, $action, $description, $table_name = null, $record_id = null, $old_values = null, $new_values = null) {
    $stmt = $pdo->prepare("INSERT INTO activity_log (user_id, action, description, table_name, record_id, old_values, new_values) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $user_id,
        $action,
        $description,
        $table_name,
        $record_id,
        $old_values ? json_encode($old_values) : null,
        $new_values ? json_encode($new_values) : null
    ]);
}

// الحصول على إعدادات المحل
function getShopSettings($pdo) {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}

// تحديث إعداد
function updateSetting($pdo, $key, $value) {
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    $stmt->execute([$key, $value, $value]);
}

// تنسيق المبلغ
function formatMoney($amount) {
    return number_format($amount, 0) . ' دينار عراقي';
}

// تنسيق التاريخ
function formatDate($date) {
    return date('Y-m-d H:i', strtotime($date));
}

// تنسيق التاريخ العربي
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    $time = date('H:i', $timestamp);
    
    return "$day $month $year - $time";
}

// إنشاء باركود
function generateBarcode() {
    return date('Ymd') . rand(1000, 9999);
}

// البحث في المنتجات
function searchProducts($pdo, $search) {
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          WHERE p.name LIKE ? OR p.barcode LIKE ? 
                          ORDER BY p.name LIMIT 20");
    $searchTerm = "%$search%";
    $stmt->execute([$searchTerm, $searchTerm]);
    return $stmt->fetchAll();
}

// الحصول على منتج بالباركود
function getProductByBarcode($pdo, $barcode) {
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          WHERE p.barcode = ?");
    $stmt->execute([$barcode]);
    return $stmt->fetch();
}

// تحديث مخزون المنتج
function updateProductStock($pdo, $product_id, $quantity, $operation = 'subtract') {
    if ($operation == 'subtract') {
        $stmt = $pdo->prepare("UPDATE products SET quantity = quantity - ? WHERE id = ?");
    } else {
        $stmt = $pdo->prepare("UPDATE products SET quantity = quantity + ? WHERE id = ?");
    }
    $stmt->execute([$quantity, $product_id]);
}

// التحقق من المخزون المنخفض
function checkLowStock($pdo) {
    $stmt = $pdo->query("SELECT * FROM products WHERE quantity <= min_quantity");
    return $stmt->fetchAll();
}

// إنشاء تنبيه
function createNotification($pdo, $type, $title, $message, $reference_id = null) {
    $stmt = $pdo->prepare("INSERT INTO notifications (type, title, message, reference_id) VALUES (?, ?, ?, ?)");
    $stmt->execute([$type, $title, $message, $reference_id]);
}

// الحصول على التنبيهات غير المقروءة
function getUnreadNotifications($pdo) {
    $stmt = $pdo->query("SELECT * FROM notifications WHERE is_read = 0 ORDER BY created_at DESC");
    return $stmt->fetchAll();
}

// تحديث رصيد العميل
function updateCustomerBalance($pdo, $customer_id, $amount, $operation = 'add') {
    if ($operation == 'add') {
        $stmt = $pdo->prepare("UPDATE customers SET balance = balance + ? WHERE id = ?");
    } else {
        $stmt = $pdo->prepare("UPDATE customers SET balance = balance - ? WHERE id = ?");
    }
    $stmt->execute([$amount, $customer_id]);
}

// الحصول على إحصائيات المبيعات
function getSalesStats($pdo, $period = 'today') {
    switch ($period) {
        case 'today':
            $where = "DATE(created_at) = CURDATE()";
            break;
        case 'week':
            $where = "WEEK(created_at) = WEEK(NOW())";
            break;
        case 'month':
            $where = "MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())";
            break;
        default:
            $where = "1=1";
    }
    
    $stmt = $pdo->query("SELECT COUNT(*) as count, SUM(total_amount) as total FROM sales WHERE $where");
    return $stmt->fetch();
}

// التحقق من طلبات الصيانة المتأخرة
function checkOverdueRepairs($pdo) {
    $stmt = $pdo->query("SELECT * FROM repairs WHERE status IN ('pending', 'in_progress') AND DATEDIFF(NOW(), created_at) > 5");
    return $stmt->fetchAll();
}

// تنظيف النص
function cleanInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// التحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// التحقق من صحة رقم الهاتف العراقي
function validateIraqiPhone($phone) {
    return preg_match('/^07[0-9]{9}$/', $phone);
}

// إنشاء رقم فاتورة
function generateInvoiceNumber($pdo) {
    $stmt = $pdo->query("SELECT MAX(id) as max_id FROM sales");
    $result = $stmt->fetch();
    $next_id = ($result['max_id'] ?? 0) + 1;
    return 'INV-' . date('Ymd') . '-' . str_pad($next_id, 4, '0', STR_PAD_LEFT);
}

// إنشاء رقم طلب صيانة
function generateRepairNumber($pdo) {
    $stmt = $pdo->query("SELECT MAX(id) as max_id FROM repairs");
    $result = $stmt->fetch();
    $next_id = ($result['max_id'] ?? 0) + 1;
    return 'REP-' . date('Ymd') . '-' . str_pad($next_id, 4, '0', STR_PAD_LEFT);
}

?>
