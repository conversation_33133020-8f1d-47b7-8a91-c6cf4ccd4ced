<?php
$notifications = getUnreadNotifications($pdo);
?>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="dashboard.php">
            <i class="fas fa-store"></i> <?= $shop_settings['shop_name'] ?? 'نظام إدارة المحل' ?>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <div class="navbar-nav ms-auto">
                <!-- التنبيهات -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if (count($notifications) > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?= count($notifications) ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 350px; max-height: 400px; overflow-y: auto;">
                        <li><h6 class="dropdown-header">التنبيهات</h6></li>
                        <?php if (count($notifications) > 0): ?>
                            <?php foreach ($notifications as $notification): ?>
                                <li>
                                    <div class="dropdown-item-text">
                                        <div class="d-flex align-items-start">
                                            <div class="flex-shrink-0 me-2">
                                                <?php if ($notification['type'] == 'low_stock'): ?>
                                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                                <?php elseif ($notification['type'] == 'overdue_repair'): ?>
                                                    <i class="fas fa-clock text-danger"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-info-circle text-info"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <strong><?= $notification['title'] ?></strong><br>
                                                <small><?= $notification['message'] ?></small><br>
                                                <small class="text-muted"><?= formatArabicDate($notification['created_at']) ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <?php if ($notification !== end($notifications)): ?>
                                    <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="notifications.php">
                                    <i class="fas fa-eye"></i> عرض جميع التنبيهات
                                </a>
                            </li>
                        <?php else: ?>
                            <li>
                                <span class="dropdown-item-text text-center text-muted">
                                    <i class="fas fa-check-circle"></i><br>
                                    لا توجد تنبيهات جديدة
                                </span>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- البحث السريع -->
                <div class="nav-item">
                    <form class="d-flex" method="GET" action="sales.php">
                        <input class="form-control form-control-sm me-2" type="search" name="search" placeholder="بحث سريع..." style="width: 200px;">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                
                <!-- المستخدم -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> <?= $_SESSION['username'] ?>
                        <?php if ($_SESSION['role'] == 'admin'): ?>
                            <span class="badge bg-warning">مدير</span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <span class="dropdown-item-text">
                                <strong><?= $_SESSION['username'] ?></strong><br>
                                <small class="text-muted"><?= $_SESSION['role'] == 'admin' ? 'مدير النظام' : 'مستخدم' ?></small>
                            </span>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
body {
    padding-top: 56px; /* ارتفاع شريط التنقل الثابت */
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.1rem;
}

.navbar-nav .nav-link {
    padding: 8px 12px;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.dropdown-item-text {
    padding: 0.5rem 1rem;
    margin-bottom: 0;
}

.badge {
    font-size: 0.65rem;
}

@media (max-width: 991.98px) {
    .navbar-nav .nav-item {
        margin: 0.25rem 0;
    }
    
    .navbar-nav form {
        margin: 0.5rem 0;
    }
}
</style>
