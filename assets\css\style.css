/* تنسيقات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 2px 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link i {
    margin-left: 8px;
    width: 16px;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 15px 15px 0 0 !important;
}

/* الحدود الملونة للبطاقات */
.border-left-primary {
    border-right: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-right: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-right: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-right: 0.25rem solid #f6c23e !important;
}

/* الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-lg {
    padding: 20px;
    font-size: 14px;
    line-height: 1.2;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 15px;
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e3e6f0;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e3e6f0;
    padding: 12px 15px;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 8px;
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* شريط التنقل */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    padding: 8px 12px;
    border-radius: 8px;
    margin: 0 4px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* القائمة المنسدلة */
.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    padding: 10px 0;
}

.dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

/* نقطة البيع */
.pos-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    padding: 20px;
    margin-bottom: 20px;
}

.product-card {
    border: 2px solid #e3e6f0;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-card:hover {
    border-color: #4e73df;
    background-color: rgba(78, 115, 223, 0.05);
}

.product-card.selected {
    border-color: #4e73df;
    background-color: rgba(78, 115, 223, 0.1);
}

/* سلة التسوق */
.cart-item {
    background: #f8f9fc;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #e3e6f0;
}

.cart-total {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
}

/* الباركود */
.barcode-container {
    width: 4.5cm;
    height: 2.5cm;
    border: 1px solid #000;
    padding: 5px;
    margin: 2px;
    display: inline-block;
    text-align: center;
    font-size: 8px;
    page-break-inside: avoid;
}

.barcode-image {
    width: 100%;
    height: 60%;
}

.barcode-text {
    font-size: 6px;
    margin-top: 2px;
}

/* الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .sidebar {
        display: none !important;
    }
    
    .navbar {
        display: none !important;
    }
    
    main {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .barcode-container {
        width: 4.5cm;
        height: 2.5cm;
        border: 1px solid #000;
        margin: 1mm;
        page-break-inside: avoid;
    }
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* التخطيط المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        top: 0;
        height: auto;
    }
    
    main {
        margin-right: 0 !important;
    }
    
    .card-body .row .col-md-6 {
        margin-bottom: 15px;
    }
    
    .btn-lg {
        padding: 15px;
        font-size: 12px;
    }
}

/* تحسينات إضافية */
.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.border-bottom-primary {
    border-bottom: 0.25rem solid #4e73df !important;
}

/* تحسين المظهر العام */
.container-fluid {
    padding-right: 1rem;
    padding-left: 1rem;
}

.main-content {
    margin-right: 250px;
    padding: 20px;
}

@media (max-width: 991.98px) {
    .main-content {
        margin-right: 0;
    }
}
