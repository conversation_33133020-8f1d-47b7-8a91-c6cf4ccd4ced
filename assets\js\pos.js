// متغيرات السلة
let cart = [];
let cartTotal = 0;

// إضافة منتج للسلة
function addToCart(product) {
    // التحقق من المخزون
    if (product.quantity <= 0) {
        alert('هذا المنتج غير متوفر في المخزون');
        return;
    }

    // البحث عن المنتج في السلة
    let existingItem = cart.find(item => item.id === product.id);
    
    if (existingItem) {
        // زيادة الكمية إذا كان المنتج موجود
        if (existingItem.quantity < product.quantity) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.selling_price;
        } else {
            alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
            return;
        }
    } else {
        // إضافة منتج جديد للسلة
        cart.push({
            id: product.id,
            name: product.name,
            selling_price: parseFloat(product.selling_price),
            quantity: 1,
            total: parseFloat(product.selling_price),
            max_quantity: product.quantity
        });
    }
    
    updateCartDisplay();
}

// حذف منتج من السلة
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
}

// تحديث كمية المنتج
function updateQuantity(productId, newQuantity) {
    let item = cart.find(item => item.id === productId);
    if (item) {
        if (newQuantity <= 0) {
            removeFromCart(productId);
        } else if (newQuantity <= item.max_quantity) {
            item.quantity = parseInt(newQuantity);
            item.total = item.quantity * item.selling_price;
            updateCartDisplay();
        } else {
            alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
            // إعادة القيمة السابقة
            document.getElementById(`quantity_${productId}`).value = item.quantity;
        }
    }
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartItemsDiv = document.getElementById('cartItems');
    const cartTotalDiv = document.getElementById('cartTotal');
    const paymentSection = document.getElementById('paymentSection');
    
    if (cart.length === 0) {
        cartItemsDiv.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        cartTotalDiv.style.display = 'none';
        paymentSection.style.display = 'none';
        return;
    }
    
    let cartHTML = '';
    let subtotal = 0;
    
    cart.forEach(item => {
        subtotal += item.total;
        cartHTML += `
            <div class="cart-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">${formatMoney(item.selling_price)} × ${item.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div class="h6 text-primary">${formatMoney(item.total)}</div>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0">الكمية:</label>
                    <input type="number" class="form-control form-control-sm" style="width: 80px;" 
                           id="quantity_${item.id}" value="${item.quantity}" min="1" max="${item.max_quantity}"
                           onchange="updateQuantity(${item.id}, this.value)">
                    <small class="text-muted ms-2">من ${item.max_quantity}</small>
                </div>
            </div>
        `;
    });
    
    cartItemsDiv.innerHTML = cartHTML;
    
    // تحديث الإجمالي
    const discount = parseFloat(document.getElementById('discount')?.value || 0);
    const total = subtotal - discount;
    
    document.getElementById('subtotal').textContent = formatMoney(subtotal);
    document.getElementById('total').textContent = formatMoney(total);
    
    cartTotal = total;
    
    cartTotalDiv.style.display = 'block';
    paymentSection.style.display = 'block';
    
    // تحديث المبلغ المدفوع ليكون مساوياً للإجمالي
    document.getElementById('paidAmount').value = total;
}

// تحديث الإجمالي عند تغيير الخصم
document.addEventListener('DOMContentLoaded', function() {
    const discountInput = document.getElementById('discount');
    if (discountInput) {
        discountInput.addEventListener('input', updateCartDisplay);
    }
    
    // التركيز على حقل الباركود
    const barcodeInput = document.getElementById('barcodeInput');
    if (barcodeInput) {
        barcodeInput.focus();
        
        // إرسال النموذج تلقائياً عند مسح الباركود
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.form.submit();
            }
        });
    }
});

// إتمام البيع
function completeSale() {
    if (cart.length === 0) {
        alert('السلة فارغة');
        return;
    }
    
    const customerId = document.getElementById('customerId').value;
    const paidAmount = parseFloat(document.getElementById('paidAmount').value || 0);
    const discount = parseFloat(document.getElementById('discount').value || 0);
    
    if (paidAmount < 0) {
        alert('المبلغ المدفوع لا يمكن أن يكون سالباً');
        return;
    }
    
    // تأكيد البيع
    const confirmMessage = `
        هل تريد إتمام البيع؟
        الإجمالي: ${formatMoney(cartTotal)}
        المدفوع: ${formatMoney(paidAmount)}
        المتبقي: ${formatMoney(cartTotal - paidAmount)}
    `;
    
    if (confirm(confirmMessage)) {
        // تعبئة النموذج
        document.getElementById('formCustomerId').value = customerId;
        document.getElementById('formCartItems').value = JSON.stringify(cart);
        document.getElementById('formTotalAmount').value = cartTotal;
        document.getElementById('formPaidAmount').value = paidAmount;
        document.getElementById('formDiscount').value = discount;
        
        // إرسال النموذج
        document.getElementById('saleForm').submit();
    }
}

// تنسيق المبلغ
function formatMoney(amount) {
    return new Intl.NumberFormat('ar-IQ').format(amount) + ' دينار عراقي';
}

// البحث السريع
function quickSearch(query) {
    if (query.length >= 2) {
        window.location.href = `sales.php?search=${encodeURIComponent(query)}`;
    }
}

// مسح الباركود
function scanBarcode(barcode) {
    if (barcode) {
        window.location.href = `sales.php?barcode=${encodeURIComponent(barcode)}`;
    }
}

// اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // F1 - التركيز على البحث
    if (e.key === 'F1') {
        e.preventDefault();
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // F2 - التركيز على الباركود
    if (e.key === 'F2') {
        e.preventDefault();
        const barcodeInput = document.getElementById('barcodeInput');
        if (barcodeInput) {
            barcodeInput.focus();
        }
    }
    
    // F3 - التركيز على اختيار العميل
    if (e.key === 'F3') {
        e.preventDefault();
        const customerSelect = document.getElementById('customerId');
        if (customerSelect) {
            customerSelect.focus();
        }
    }
    
    // F4 - إتمام البيع
    if (e.key === 'F4') {
        e.preventDefault();
        completeSale();
    }
    
    // Escape - مسح السلة
    if (e.key === 'Escape') {
        if (cart.length > 0 && confirm('هل تريد مسح السلة؟')) {
            cart = [];
            updateCartDisplay();
        }
    }
});

// تحديث معلومات العميل عند الاختيار
document.addEventListener('DOMContentLoaded', function() {
    const customerSelect = document.getElementById('customerId');
    if (customerSelect) {
        customerSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const balance = selectedOption.getAttribute('data-balance');
            
            // يمكن إضافة عرض معلومات العميل هنا
            if (balance && parseFloat(balance) > 0) {
                console.log(`العميل مديون بمبلغ: ${formatMoney(balance)}`);
            }
        });
    }
});

// حفظ السلة في التخزين المحلي
function saveCartToStorage() {
    localStorage.setItem('pos_cart', JSON.stringify(cart));
}

// استرداد السلة من التخزين المحلي
function loadCartFromStorage() {
    const savedCart = localStorage.getItem('pos_cart');
    if (savedCart) {
        cart = JSON.parse(savedCart);
        updateCartDisplay();
    }
}

// مسح السلة من التخزين المحلي
function clearCartFromStorage() {
    localStorage.removeItem('pos_cart');
}

// حفظ السلة عند كل تحديث
const originalUpdateCartDisplay = updateCartDisplay;
updateCartDisplay = function() {
    originalUpdateCartDisplay();
    saveCartToStorage();
};

// تحميل السلة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadCartFromStorage();
});
