<?php
session_start();

// التحقق من وجود ملف الإعدادات
if (file_exists('config/database.php')) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

if ($_POST) {
    $db_host = $_POST['db_host'] ?? '';
    $db_name = $_POST['db_name'] ?? '';
    $db_user = $_POST['db_user'] ?? '';
    $db_pass = $_POST['db_pass'] ?? '';
    $admin_username = $_POST['admin_username'] ?? '';
    $admin_password = $_POST['admin_password'] ?? '';
    $shop_name = $_POST['shop_name'] ?? '';
    $shop_phone = $_POST['shop_phone'] ?? '';
    $shop_address = $_POST['shop_address'] ?? '';
    
    if (empty($db_host) || empty($db_name) || empty($db_user) || empty($admin_username) || empty($admin_password)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        try {
            // اختبار الاتصال بقاعدة البيانات
            $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$db_name`");
            
            // إنشاء الجداول
            include 'install/create_tables.php';
            createTables($pdo);
            
            // إنشاء المستخدم الأساسي
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, password, role, created_at) VALUES (?, ?, 'admin', NOW())");
            $stmt->execute([$admin_username, $hashed_password]);
            
            // إنشاء إعدادات المحل
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES 
                ('shop_name', ?), ('shop_phone', ?), ('shop_address', ?), ('currency', 'دينار عراقي')");
            $stmt->execute([$shop_name, $shop_phone, $shop_address]);
            
            // إنشاء ملف الإعدادات
            if (!is_dir('config')) {
                mkdir('config', 0755, true);
            }
            
            $config_content = "<?php
define('DB_HOST', '$db_host');
define('DB_NAME', '$db_name');
define('DB_USER', '$db_user');
define('DB_PASS', '$db_pass');

try {
    \$pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException \$e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . \$e->getMessage());
}
?>";
            
            file_put_contents('config/database.php', $config_content);
            
            $success = 'تم تثبيت النظام بنجاح! يمكنك الآن تسجيل الدخول';
            
        } catch (Exception $e) {
            $error = 'خطأ في التثبيت: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المحل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .install-body {
            padding: 40px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            margin-bottom: 20px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-install {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: bold;
            width: 100%;
        }
        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .section-title {
            color: #667eea;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 25px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <h1><i class="fas fa-store"></i> نظام إدارة المحل</h1>
                <p>مرحباً بك في تثبيت نظام إدارة المحل الشامل</p>
            </div>
            
            <div class="install-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> <?= $error ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= $success ?>
                        <br><br>
                        <a href="index.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt"></i> الانتقال لتسجيل الدخول
                        </a>
                    </div>
                <?php else: ?>
                    <form method="POST">
                        <h4 class="section-title">
                            <i class="fas fa-database"></i> إعدادات قاعدة البيانات
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">خادم قاعدة البيانات</label>
                                <input type="text" name="db_host" class="form-control" value="localhost" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">اسم قاعدة البيانات</label>
                                <input type="text" name="db_name" class="form-control" placeholder="shop_system" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" name="db_user" class="form-control" placeholder="root" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" name="db_pass" class="form-control">
                            </div>
                        </div>
                        
                        <h4 class="section-title">
                            <i class="fas fa-user-shield"></i> المستخدم الأساسي
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" name="admin_username" class="form-control" placeholder="admin" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" name="admin_password" class="form-control" required>
                            </div>
                        </div>
                        
                        <h4 class="section-title">
                            <i class="fas fa-store"></i> معلومات المحل
                        </h4>
                        
                        <label class="form-label">اسم المحل</label>
                        <input type="text" name="shop_name" class="form-control" placeholder="محل اكسسوارات الموبايل" required>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" name="shop_phone" class="form-control" placeholder="07xxxxxxxxx">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">العنوان</label>
                                <input type="text" name="shop_address" class="form-control" placeholder="بغداد - العراق">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-install">
                            <i class="fas fa-download"></i> تثبيت النظام
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
