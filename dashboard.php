<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkLogin();

$shop_settings = getShopSettings($pdo);
$notifications = getUnreadNotifications($pdo);
$today_stats = getSalesStats($pdo, 'today');
$month_stats = getSalesStats($pdo, 'month');
$low_stock = checkLowStock($pdo);
$overdue_repairs = checkOverdueRepairs($pdo);

// إنشاء تنبيهات للمخزون المنخفض
foreach ($low_stock as $product) {
    $existing = $pdo->prepare("SELECT id FROM notifications WHERE type = 'low_stock' AND reference_id = ? AND is_read = 0");
    $existing->execute([$product['id']]);
    if (!$existing->fetch()) {
        createNotification($pdo, 'low_stock', 'مخزون منخفض', "المنتج '{$product['name']}' وصل إلى الحد الأدنى للمخزون", $product['id']);
    }
}

// إنشاء تنبيهات للصيانة المتأخرة
foreach ($overdue_repairs as $repair) {
    $existing = $pdo->prepare("SELECT id FROM notifications WHERE type = 'overdue_repair' AND reference_id = ? AND is_read = 0");
    $existing->execute([$repair['id']]);
    if (!$existing->fetch()) {
        createNotification($pdo, 'overdue_repair', 'صيانة متأخرة', "طلب الصيانة رقم {$repair['id']} متأخر أكثر من 5 أيام", $repair['id']);
    }
}

// تحديث التنبيهات
$notifications = getUnreadNotifications($pdo);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?= $shop_settings['shop_name'] ?? 'نظام إدارة المحل' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-store"></i> <?= $shop_settings['shop_name'] ?? 'نظام إدارة المحل' ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <!-- التنبيهات -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if (count($notifications) > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?= count($notifications) ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                        <?php if (count($notifications) > 0): ?>
                            <?php foreach ($notifications as $notification): ?>
                                <li>
                                    <div class="dropdown-item-text">
                                        <strong><?= $notification['title'] ?></strong><br>
                                        <small><?= $notification['message'] ?></small><br>
                                        <small class="text-muted"><?= formatArabicDate($notification['created_at']) ?></small>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                            <?php endforeach; ?>
                            <li><a class="dropdown-item text-center" href="notifications.php">عرض جميع التنبيهات</a></li>
                        <?php else: ?>
                            <li><span class="dropdown-item-text">لا توجد تنبيهات جديدة</span></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- المستخدم -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?= $_SESSION['username'] ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sales.php">
                                <i class="fas fa-cash-register"></i> نقطة البيع
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="products.php">
                                <i class="fas fa-box"></i> إدارة المنتجات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users"></i> إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="repairs.php">
                                <i class="fas fa-tools"></i> إدارة الصيانة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="invoices.php">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="barcode.php">
                                <i class="fas fa-barcode"></i> طباعة الباركود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i> التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="activity_log.php">
                                <i class="fas fa-history"></i> سجل النشاطات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة التحكم</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-calendar"></i> <?= date('Y-m-d') ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            مبيعات اليوم
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= formatMoney($today_stats['total'] ?? 0) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            مبيعات الشهر
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= formatMoney($month_stats['total'] ?? 0) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            منتجات بمخزون منخفض
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= count($low_stock) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            طلبات صيانة متأخرة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= count($overdue_repairs) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-tools fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <a href="sales.php" class="btn btn-primary btn-lg w-100">
                                            <i class="fas fa-cash-register"></i><br>
                                            بيع جديد
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a href="products.php?action=add" class="btn btn-success btn-lg w-100">
                                            <i class="fas fa-plus"></i><br>
                                            إضافة منتج
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a href="customers.php?action=add" class="btn btn-info btn-lg w-100">
                                            <i class="fas fa-user-plus"></i><br>
                                            عميل جديد
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a href="repairs.php?action=add" class="btn btn-warning btn-lg w-100">
                                            <i class="fas fa-tools"></i><br>
                                            طلب صيانة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">آخر النشاطات</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $stmt = $pdo->prepare("SELECT * FROM activity_log ORDER BY created_at DESC LIMIT 5");
                                $stmt->execute();
                                $activities = $stmt->fetchAll();
                                ?>
                                <?php if ($activities): ?>
                                    <?php foreach ($activities as $activity): ?>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-circle text-primary" style="font-size: 0.5rem;"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div class="small"><?= $activity['description'] ?></div>
                                                <div class="text-muted" style="font-size: 0.75rem;">
                                                    <?= formatArabicDate($activity['created_at']) ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="activity_log.php" class="btn btn-sm btn-outline-primary">عرض المزيد</a>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">لا توجد نشاطات حديثة</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
