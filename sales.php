<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkLogin();

$shop_settings = getShopSettings($pdo);

// معالجة البحث
$search_results = [];
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search_results = searchProducts($pdo, $_GET['search']);
}

// معالجة البحث بالباركود
if (isset($_GET['barcode']) && !empty($_GET['barcode'])) {
    $product = getProductByBarcode($pdo, $_GET['barcode']);
    if ($product) {
        $search_results = [$product];
    }
}

// الحصول على العملاء
$stmt = $pdo->query("SELECT * FROM customers ORDER BY name");
$customers = $stmt->fetchAll();

// معالجة إتمام البيع
if ($_POST && isset($_POST['complete_sale'])) {
    $customer_id = $_POST['customer_id'] ?? null;
    $cart_items = json_decode($_POST['cart_items'], true);
    $total_amount = $_POST['total_amount'];
    $paid_amount = $_POST['paid_amount'];
    $discount = $_POST['discount'] ?? 0;
    
    if (!empty($cart_items)) {
        try {
            $pdo->beginTransaction();
            
            // إنشاء المبيعة
            $payment_status = ($paid_amount >= $total_amount) ? 'paid' : (($paid_amount > 0) ? 'partial' : 'unpaid');
            
            $stmt = $pdo->prepare("INSERT INTO sales (customer_id, total_amount, paid_amount, discount, payment_status, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$customer_id, $total_amount, $paid_amount, $discount, $payment_status]);
            $sale_id = $pdo->lastInsertId();
            
            // إضافة عناصر المبيعة
            foreach ($cart_items as $item) {
                $stmt = $pdo->prepare("INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$sale_id, $item['id'], $item['quantity'], $item['selling_price'], $item['total']]);
                
                // تحديث المخزون
                updateProductStock($pdo, $item['id'], $item['quantity']);
            }
            
            // تحديث رصيد العميل إذا كان هناك مبلغ متبقي
            if ($customer_id && $paid_amount < $total_amount) {
                $remaining = $total_amount - $paid_amount;
                updateCustomerBalance($pdo, $customer_id, $remaining, 'add');
            }
            
            // تسجيل الدفعة
            if ($paid_amount > 0 && $customer_id) {
                $stmt = $pdo->prepare("INSERT INTO payments (customer_id, type, reference_id, amount, payment_method, created_at) VALUES (?, 'sale', ?, ?, 'cash', NOW())");
                $stmt->execute([$customer_id, $sale_id, $paid_amount]);
            }
            
            // تسجيل النشاط
            logActivity($pdo, $_SESSION['user_id'], 'sale_created', "تم إنشاء مبيعة جديدة برقم $sale_id", 'sales', $sale_id);
            
            $pdo->commit();
            
            // إعادة توجيه لطباعة الفاتورة
            header("Location: print_invoice.php?sale_id=$sale_id");
            exit();
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error = 'خطأ في إتمام البيع: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع - <?= $shop_settings['shop_name'] ?? 'نظام إدارة المحل' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-cash-register"></i> نقطة البيع</h1>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> <?= $error ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- قسم البحث والمنتجات -->
                    <div class="col-lg-8">
                        <div class="pos-container">
                            <h5><i class="fas fa-search"></i> البحث عن المنتجات</h5>
                            
                            <!-- شريط البحث -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <form method="GET" class="d-flex">
                                        <input type="text" name="search" class="form-control" placeholder="ابحث عن منتج..." value="<?= $_GET['search'] ?? '' ?>">
                                        <button type="submit" class="btn btn-primary ms-2">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <form method="GET" class="d-flex">
                                        <input type="text" name="barcode" class="form-control" placeholder="امسح الباركود..." value="<?= $_GET['barcode'] ?? '' ?>" id="barcodeInput">
                                        <button type="submit" class="btn btn-success ms-2">
                                            <i class="fas fa-barcode"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- نتائج البحث -->
                            <div class="row" id="searchResults">
                                <?php if (!empty($search_results)): ?>
                                    <?php foreach ($search_results as $product): ?>
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="product-card" onclick="addToCart(<?= htmlspecialchars(json_encode($product)) ?>)">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?= $product['name'] ?></h6>
                                                        <small class="text-muted"><?= $product['category_name'] ?? 'غير محدد' ?></small>
                                                        <br>
                                                        <small class="text-muted">الباركود: <?= $product['barcode'] ?></small>
                                                    </div>
                                                    <div class="text-end">
                                                        <div class="h6 text-primary"><?= formatMoney($product['selling_price']) ?></div>
                                                        <small class="text-muted">المخزون: <?= $product['quantity'] ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php elseif (isset($_GET['search']) || isset($_GET['barcode'])): ?>
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i> لم يتم العثور على منتجات
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-search fa-3x mb-3"></i>
                                            <p>ابحث عن منتج أو امسح الباركود لبدء البيع</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- سلة التسوق -->
                    <div class="col-lg-4">
                        <div class="pos-container">
                            <h5><i class="fas fa-shopping-cart"></i> سلة التسوق</h5>
                            
                            <!-- اختيار العميل -->
                            <div class="mb-3">
                                <label class="form-label">العميل</label>
                                <select class="form-select" id="customerId">
                                    <option value="">عميل جديد</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?= $customer['id'] ?>" data-balance="<?= $customer['balance'] ?>">
                                            <?= $customer['name'] ?> 
                                            <?php if ($customer['balance'] > 0): ?>
                                                (مديون: <?= formatMoney($customer['balance']) ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- عناصر السلة -->
                            <div id="cartItems">
                                <div class="text-center text-muted">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                    <p>السلة فارغة</p>
                                </div>
                            </div>

                            <!-- الإجمالي -->
                            <div class="cart-total mt-3" id="cartTotal" style="display: none;">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotal">0</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم:</span>
                                    <input type="number" class="form-control form-control-sm d-inline-block" style="width: 100px;" id="discount" value="0" min="0">
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <span>الإجمالي:</span>
                                    <span id="total">0</span>
                                </div>
                            </div>

                            <!-- الدفع -->
                            <div class="mt-3" id="paymentSection" style="display: none;">
                                <label class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paidAmount" placeholder="0" min="0">
                                
                                <button type="button" class="btn btn-success w-100 mt-3" onclick="completeSale()">
                                    <i class="fas fa-check"></i> إتمام البيع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نموذج إتمام البيع -->
    <form id="saleForm" method="POST" style="display: none;">
        <input type="hidden" name="complete_sale" value="1">
        <input type="hidden" name="customer_id" id="formCustomerId">
        <input type="hidden" name="cart_items" id="formCartItems">
        <input type="hidden" name="total_amount" id="formTotalAmount">
        <input type="hidden" name="paid_amount" id="formPaidAmount">
        <input type="hidden" name="discount" id="formDiscount">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/pos.js"></script>
</body>
</html>
