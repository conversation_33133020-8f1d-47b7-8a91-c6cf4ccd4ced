<?php
// تحديد الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>
<nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'dashboard' ? 'active' : '' ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'sales' ? 'active' : '' ?>" href="sales.php">
                    <i class="fas fa-cash-register"></i>
                    نقطة البيع
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'products' ? 'active' : '' ?>" href="products.php">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'customers' ? 'active' : '' ?>" href="customers.php">
                    <i class="fas fa-users"></i>
                    إدارة العملاء
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'repairs' ? 'active' : '' ?>" href="repairs.php">
                    <i class="fas fa-tools"></i>
                    إدارة الصيانة
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'invoices' ? 'active' : '' ?>" href="invoices.php">
                    <i class="fas fa-file-invoice"></i>
                    الفواتير
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'barcode' ? 'active' : '' ?>" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    طباعة الباركود
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'reports' ? 'active' : '' ?>" href="reports.php">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'notifications' ? 'active' : '' ?>" href="notifications.php">
                    <i class="fas fa-bell"></i>
                    التنبيهات
                    <?php
                    $unread_count = count(getUnreadNotifications($pdo));
                    if ($unread_count > 0):
                    ?>
                        <span class="badge bg-danger ms-2"><?= $unread_count ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= $current_page == 'activity_log' ? 'active' : '' ?>" href="activity_log.php">
                    <i class="fas fa-history"></i>
                    سجل النشاطات
                </a>
            </li>
            
            <?php if ($_SESSION['role'] == 'admin'): ?>
                <li class="nav-item mt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>إعدادات المدير</span>
                    </h6>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= $current_page == 'settings' ? 'active' : '' ?>" href="settings.php">
                        <i class="fas fa-cog"></i>
                        إعدادات النظام
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= $current_page == 'users' ? 'active' : '' ?>" href="users.php">
                        <i class="fas fa-user-shield"></i>
                        إدارة المستخدمين
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= $current_page == 'backup' ? 'active' : '' ?>" href="backup.php">
                        <i class="fas fa-database"></i>
                        النسخ الاحتياطي
                    </a>
                </li>
            <?php endif; ?>
        </ul>
        
        <!-- معلومات سريعة -->
        <div class="mt-4 px-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body p-3">
                    <h6 class="card-title mb-2">
                        <i class="fas fa-info-circle"></i> معلومات سريعة
                    </h6>
                    <small>
                        <?php
                        $today_sales = getSalesStats($pdo, 'today');
                        $low_stock_count = count(checkLowStock($pdo));
                        ?>
                        <div class="mb-1">
                            <i class="fas fa-chart-line"></i> مبيعات اليوم: <?= $today_sales['count'] ?? 0 ?>
                        </div>
                        <div class="mb-1">
                            <i class="fas fa-exclamation-triangle"></i> مخزون منخفض: <?= $low_stock_count ?>
                        </div>
                        <div>
                            <i class="fas fa-clock"></i> <?= date('H:i') ?>
                        </div>
                    </small>
                </div>
            </div>
        </div>
        
        <!-- اختصارات لوحة المفاتيح -->
        <div class="mt-3 px-3">
            <div class="card border-0 bg-light">
                <div class="card-body p-3">
                    <h6 class="card-title mb-2">
                        <i class="fas fa-keyboard"></i> اختصارات
                    </h6>
                    <small class="text-muted">
                        <div>F1 - البحث</div>
                        <div>F2 - الباركود</div>
                        <div>F3 - العميل</div>
                        <div>F4 - إتمام البيع</div>
                    </small>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 20px 0 0;
    box-shadow: inset 1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 2px 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateX(-2px);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.15);
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-left: 8px;
    width: 18px;
    text-align: center;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge {
    font-size: 0.65rem;
}

/* تحسين المظهر للشاشات الصغيرة */
@media (max-width: 767.98px) {
    .sidebar {
        position: relative;
        top: 0;
        height: auto;
        padding: 10px 0;
    }
    
    .sidebar .nav-link {
        margin: 1px 8px;
        padding: 10px 16px;
    }
    
    .sidebar .card {
        margin: 10px 8px;
    }
}

/* تحسين التمرير */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تأثيرات إضافية */
.sidebar .card {
    transition: all 0.3s ease;
}

.sidebar .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
