<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkLogin();

$sale_id = $_GET['sale_id'] ?? 0;

if (!$sale_id) {
    header('Location: sales.php');
    exit();
}

// الحصول على بيانات المبيعة
$stmt = $pdo->prepare("SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address 
                       FROM sales s 
                       LEFT JOIN customers c ON s.customer_id = c.id 
                       WHERE s.id = ?");
$stmt->execute([$sale_id]);
$sale = $stmt->fetch();

if (!$sale) {
    header('Location: sales.php');
    exit();
}

// الحصول على عناصر المبيعة
$stmt = $pdo->prepare("SELECT si.*, p.name as product_name, p.barcode 
                       FROM sale_items si 
                       JOIN products p ON si.product_id = p.id 
                       WHERE si.sale_id = ?");
$stmt->execute([$sale_id]);
$sale_items = $stmt->fetchAll();

// الحصول على إعدادات المحل
$shop_settings = getShopSettings($pdo);

// إنشاء رقم الفاتورة
$invoice_number = 'INV-' . date('Ymd', strtotime($sale['created_at'])) . '-' . str_pad($sale['id'], 4, '0', STR_PAD_LEFT);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم <?= $invoice_number ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; padding: 15px; }
            .invoice { box-shadow: none !important; border: 1px solid #000 !important; }
        }
        
        .invoice {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .invoice-body {
            padding: 30px;
        }
        
        .invoice-table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }
        
        .invoice-total {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .shop-info {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .customer-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .invoice-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- أزرار التحكم -->
        <div class="row no-print mb-3">
            <div class="col-12 text-center">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print"></i> طباعة الفاتورة
                </button>
                <a href="sales.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للمبيعات
                </a>
                <a href="dashboard.php" class="btn btn-info">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
            </div>
        </div>

        <!-- الفاتورة -->
        <div class="invoice">
            <!-- رأس الفاتورة -->
            <div class="invoice-header">
                <h1><i class="fas fa-file-invoice"></i> فاتورة مبيعات</h1>
                <h3>رقم الفاتورة: <?= $invoice_number ?></h3>
                <p>تاريخ الإصدار: <?= formatArabicDate($sale['created_at']) ?></p>
            </div>

            <div class="invoice-body">
                <!-- معلومات المحل -->
                <div class="shop-info">
                    <h2><?= $shop_settings['shop_name'] ?? 'نظام إدارة المحل' ?></h2>
                    <?php if (!empty($shop_settings['shop_phone'])): ?>
                        <p><i class="fas fa-phone"></i> <?= $shop_settings['shop_phone'] ?></p>
                    <?php endif; ?>
                    <?php if (!empty($shop_settings['shop_address'])): ?>
                        <p><i class="fas fa-map-marker-alt"></i> <?= $shop_settings['shop_address'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- معلومات العميل -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="customer-info">
                            <h5><i class="fas fa-user"></i> معلومات العميل</h5>
                            <?php if ($sale['customer_name']): ?>
                                <p><strong>الاسم:</strong> <?= $sale['customer_name'] ?></p>
                                <?php if ($sale['customer_phone']): ?>
                                    <p><strong>الهاتف:</strong> <?= $sale['customer_phone'] ?></p>
                                <?php endif; ?>
                                <?php if ($sale['customer_address']): ?>
                                    <p><strong>العنوان:</strong> <?= $sale['customer_address'] ?></p>
                                <?php endif; ?>
                            <?php else: ?>
                                <p>عميل جديد</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="customer-info">
                            <h5><i class="fas fa-info-circle"></i> تفاصيل الفاتورة</h5>
                            <p><strong>رقم الفاتورة:</strong> <?= $invoice_number ?></p>
                            <p><strong>التاريخ:</strong> <?= formatArabicDate($sale['created_at']) ?></p>
                            <p><strong>حالة الدفع:</strong> 
                                <?php
                                switch($sale['payment_status']) {
                                    case 'paid': echo '<span class="badge bg-success">مدفوع</span>'; break;
                                    case 'partial': echo '<span class="badge bg-warning">مدفوع جزئياً</span>'; break;
                                    case 'unpaid': echo '<span class="badge bg-danger">غير مدفوع</span>'; break;
                                }
                                ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="table-responsive">
                    <table class="table table-bordered invoice-table">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="40%">المنتج</th>
                                <th width="15%">الباركود</th>
                                <th width="10%">الكمية</th>
                                <th width="15%">السعر</th>
                                <th width="15%">الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $counter = 1; ?>
                            <?php foreach ($sale_items as $item): ?>
                                <tr>
                                    <td><?= $counter++ ?></td>
                                    <td><?= $item['product_name'] ?></td>
                                    <td><?= $item['barcode'] ?></td>
                                    <td><?= $item['quantity'] ?></td>
                                    <td><?= formatMoney($item['unit_price']) ?></td>
                                    <td><?= formatMoney($item['total_price']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- الإجمالي -->
                <div class="row">
                    <div class="col-md-6 ms-auto">
                        <div class="invoice-total">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>المجموع الفرعي:</strong></td>
                                    <td class="text-end"><strong><?= formatMoney($sale['total_amount'] + $sale['discount']) ?></strong></td>
                                </tr>
                                <?php if ($sale['discount'] > 0): ?>
                                    <tr>
                                        <td><strong>الخصم:</strong></td>
                                        <td class="text-end"><strong>- <?= formatMoney($sale['discount']) ?></strong></td>
                                    </tr>
                                <?php endif; ?>
                                <tr class="border-top">
                                    <td><h5><strong>الإجمالي النهائي:</strong></h5></td>
                                    <td class="text-end"><h5><strong><?= formatMoney($sale['total_amount']) ?></strong></h5></td>
                                </tr>
                                <tr>
                                    <td><strong>المبلغ المدفوع:</strong></td>
                                    <td class="text-end"><strong><?= formatMoney($sale['paid_amount']) ?></strong></td>
                                </tr>
                                <?php if ($sale['total_amount'] > $sale['paid_amount']): ?>
                                    <tr class="text-danger">
                                        <td><strong>المبلغ المتبقي:</strong></td>
                                        <td class="text-end"><strong><?= formatMoney($sale['total_amount'] - $sale['paid_amount']) ?></strong></td>
                                    </tr>
                                <?php elseif ($sale['paid_amount'] > $sale['total_amount']): ?>
                                    <tr class="text-success">
                                        <td><strong>المبلغ المرتجع:</strong></td>
                                        <td class="text-end"><strong><?= formatMoney($sale['paid_amount'] - $sale['total_amount']) ?></strong></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <?php if (!empty($sale['notes'])): ?>
                    <div class="mt-4">
                        <h6><i class="fas fa-sticky-note"></i> ملاحظات:</h6>
                        <p class="border p-3 rounded"><?= nl2br(htmlspecialchars($sale['notes'])) ?></p>
                    </div>
                <?php endif; ?>

                <!-- تذييل الفاتورة -->
                <div class="invoice-footer">
                    <p><strong>شكراً لتعاملكم معنا</strong></p>
                    <p>هذه فاتورة إلكترونية صادرة من نظام إدارة المحل</p>
                    <small>تم الطباعة في: <?= date('Y-m-d H:i:s') ?></small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
        
        // اختصار لوحة المفاتيح للطباعة
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
